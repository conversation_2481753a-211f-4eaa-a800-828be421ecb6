<template>
    <div class="ship-detail fullscreen-overlay">
        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-header">
            <button @click="toggleMenu" class="menu-btn">☰</button>
            <button @click="goBack" class="back-btn"></button>
        </div>

        <!-- 整体布局：左侧菜单 + 右侧内容 -->
        <div class="detail-layout">
            <!-- 左侧菜单（移动端时变为侧滑抽屉） -->
            <div class="menu-sidebar" :class="{ 'mobile-visible': menuVisible }">
                <div class="sidebar-header">
                    <span>菜单</span>
                    <button @click="toggleMenu" class="close-menu-btn">×</button>
                </div>
                <div v-for="tab in tabs" :key="tab.id" :class="{ 'menu-item': true, active: activeTab === tab.id }"
                    @click="setActiveTab(tab.id)">
                    {{ tab.name }}
                </div>
            </div>

            <!-- 右侧详情内容区域 -->
            <div class="content-area">
                <!-- 在线船舶列表 -->
                <div v-if="activeTab === 'online'" class="info-section">
                    <h2>沪籍在线船舶/渔船总数</h2>

                    <!-- 操作栏 -->
                    <div class="toolbar">
                        <div class="toolbar-left">
                            <button class="tool-btn active">导出</button>
                            <button class="tool-btn">北斗通信</button>
                            <button class="tool-btn">显示离线船舶</button>
                        </div>
                        <div class="toolbar-right">
                            <span class="count-display">总数: {{ shipList.length }}</span>
                        </div>
                    </div>

                    <!-- 竖向表格容器 -->
                    <div class="vertical-table-container">
                        <div v-if="loading" class="loading-message">
                            <span>加载中...</span>
                        </div>

                        <div v-else-if="shipList.length === 0" class="empty-message">
                            <span>暂无数据</span>
                        </div>

                        <div v-else class="vertical-table">
                            <!-- 表头列（左侧垂直） -->
                            <div class="table-headers">
                                <div class="header-cell">序号</div>
                                <div class="header-cell">船名</div>
                                <div class="header-cell">船舶类型</div>
                                <div class="header-cell">组织机构</div>
                                <div class="header-cell">呼号</div>
                                <div class="header-cell">船长</div>
                                <div class="header-cell">型宽</div>
                                <div class="header-cell">作业场所</div>
                                <div class="header-cell">属地</div>
                                <div class="header-cell">作业类型</div>
                                <div class="header-cell">船舶所有人</div>
                                <div class="header-cell">功率</div>
                                <div class="header-cell">吨位</div>
                                <div class="header-cell">建造完工日期</div>
                                <div class="header-cell">船体材料</div>
                                <div class="header-cell">联系电话</div>
                                <div class="header-cell">北斗号</div>
                                <div class="header-cell">MMSI</div>
                                <div class="header-cell">离线原因</div>
                            </div>

                            <!-- 数据区域（右侧横向滚动） -->
                            <div class="table-data-container">
                                <div class="table-data">
                                    <!-- 每一列代表一条船舶数据 -->
                                    <div v-for="(ship, index) in shipList"
                                         :key="ship.id || index"
                                         class="data-column"
                                         @click="handleShipClick(ship)">
                                        <div class="data-cell">{{ index + 1 }}</div>
                                        <div class="data-cell ship-name-cell">{{ ship.shipName || '-' }}</div>
                                        <div class="data-cell">{{ ship.shipType || '国内捕捞船' }}</div>
                                        <div class="data-cell">{{ ship.organization || '崇明区' }}</div>
                                        <div class="data-cell">{{ ship.callSign || '-' }}</div>
                                        <div class="data-cell">{{ ship.length || '32.99' }}米</div>
                                        <div class="data-cell">{{ ship.width || '6.3' }}米</div>
                                        <div class="data-cell">{{ ship.operationArea || 'C2渔区' }}</div>
                                        <div class="data-cell">{{ ship.location || '崇明区' }}</div>
                                        <div class="data-cell">{{ ship.operationType || '拖网' }}</div>
                                        <div class="data-cell">{{ ship.owner || '-' }}</div>
                                        <div class="data-cell">{{ ship.power || '232' }}千瓦</div>
                                        <div class="data-cell">{{ ship.tonnage || '185' }}吨</div>
                                        <div class="data-cell">{{ ship.completionDate || '2015-06-24' }}</div>
                                        <div class="data-cell">{{ ship.hullMaterial || '钢质' }}</div>
                                        <div class="data-cell">{{ ship.phone || '-' }}</div>
                                        <div class="data-cell">{{ ship.beidouNo || '-' }}</div>
                                        <div class="data-cell">{{ ship.mmsi || '-' }}</div>
                                        <div class="data-cell">{{ ship.offlineReason || '-' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 离线船舶列表 -->
                <div v-if="activeTab === 'offline'" class="info-section">
                    <h2>离线船舶列表</h2>

                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="toolbar-left">
                            <button class="tool-btn active">导出</button>
                            <button class="tool-btn">北斗通信</button>
                        </div>
                        <div class="toolbar-right">
                            <span class="count-display">总数: {{ selectShipInfoList.length }}</span>
                        </div>
                    </div>

                    <!-- 竖向表格容器 -->
                    <div class="vertical-table-container">
                        <div v-if="loading" class="loading-message">
                            <span>加载中...</span>
                        </div>

                        <div v-else class="offline-horizontal-table">
                            <!-- 传统表格布局 -->
                            <table class="offline-ship-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>船舶名称</th>
                                        <th>离线时间</th>
                                        <th>船舶所有人</th>
                                        <th>联系电话</th>
                                        <th>北斗号</th>
                                        <th>MMSI</th>
                                        <th>离线原因</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 有数据时显示船舶列表 -->
                                    <tr v-for="(ship, index) in selectShipInfoList"
                                        :key="ship.shipId || index"
                                        class="offline-ship-row"
                                        @click="handleShipClick(ship)">
                                        <td>{{ index + 1 }}</td>
                                        <td class="ship-name-cell">{{ ship.shipName || '-' }}</td>
                                        <td :class="{'offline-time-cell': ship.outLineColor === 1}">{{ ship.outTime || '-' }}</td>
                                        <td>{{ ship.owner || '-' }}</td>
                                        <td>{{ ship.phone || '-' }}</td>
                                        <td>{{ ship.shortBdId || ship.bdId || '-' }}</td>
                                        <td>{{ ship.mmsi || '-' }}</td>
                                        <td class="reason-cell">{{ ship.outLineReason || '-' }}</td>
                                    </tr>
                                    <!-- 无数据时显示占位行 -->
                                    <tr v-if="selectShipInfoList.length === 0" class="empty-row">
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div v-if="activeTab === 'statistics'" class="info-section">
                    <h2>船舶统计信息</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">在线船舶数：</span>
                            <span class="value">{{ shipList.length }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">离线船舶数：</span>
                            <span class="value">{{ offlineShipCount }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">总船舶数：</span>
                            <span class="value">{{ totalShipCount }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">在线率：</span>
                            <span class="value">{{ onlineRate }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import global from './Global.vue';

export default {
    name: 'OnlineShipListCard',
    data() {
        return {
            shipList: [
                {
                    id: 1,
                    shipName: '沪崇渔11003',
                    portName: '吴家港',
                    owner: '尹建飞',
                    phone: '13816807592',
                    beidouNo: '203131...',
                    mmsi: '412370392',
                    offlineReason: '-'
                },
                {
                    id: 2,
                    shipName: '沪崇渔11005',
                    portName: '吴家港',
                    owner: '张秀林',
                    phone: '13040664848',
                    beidouNo: '392748...',
                    mmsi: '412370304',
                    offlineReason: '-'
                },
                {
                    id: 3,
                    shipName: '沪崇渔11006',
                    portName: '吴家港',
                    owner: '张秀林',
                    phone: '13040664848',
                    beidouNo: '397128...',
                    mmsi: '412370317',
                    offlineReason: '-'
                }
            ],
            loading: false,
            menuVisible: false,
            activeTab: 'online',
            offlineShipCount: 0,
            totalShipCount: 0,
            // 离线船舶相关数据属性
            selectShipInfoList: [], // 离线船舶列表数据
            curSelecctShipCount: 0, // 当前选中船舶数量
            mapShipShowOrNot: false, // 地图船舶显示状态
            portOrPo: true, // 港口或位置显示状态
            showOutLineShip: true, // 显示离线船舶状态
            ifFishArea: false, // 是否渔区状态
            ShipSearch1IsSow: true, // 船舶搜索1显示状态
            tabs: [
                { id: 'online', name: '在线船舶' },
                { id: 'offline', name: '离线船舶' },
                { id: 'statistics', name: '统计信息' }
            ]
        }
    },

    computed: {
        onlineRate() {
            if (this.totalShipCount === 0) return 0;
            return Math.round((this.shipList.length / this.totalShipCount) * 100);
        }
    },

    mounted() {
        this.loadShipList();
    },

    methods: {
        // 加载船舶列表数据
        loadShipList() {
            const _this = this;
            // _this.loading = true;

            // // 这里调用获取在线船舶列表的API
            // $.get(global.IP + "/web/GetOnlineShipList", function (data) {
            //     _this.shipList = data.map((ship, index) => ({
            //         id: ship.id || index,
            //         shipName: ship.shipName,
            //         portName: ship.portName,
            //         owner: ship.owner,
            //         phone: ship.phone,
            //         beidouNo: ship.beidouNo,
            //         mmsi: ship.mmsi,
            //         offlineReason: ship.offlineReason || '-'
            //     }));

            //     console.log('在线船舶列表已加载:', _this.shipList);
            // }).fail(function (error) {
            //     console.error('获取在线船舶列表失败:', error);
            // }).always(() => {
            //     _this.loading = false;
            // });
        },

        // 加载离线船舶列表
        loadOfflineShipList() {
            console.log('开始加载离线船舶列表...');
            this.loading = true; // 设置加载状态
            // 查询离线船舶 - 尝试不同的参数
            // 先尝试原来的参数，如果没有数据再尝试其他参数
            this.GetAllOutShipInfo('all');

            // 如果all参数没有数据，可以尝试以下参数：
            // this.GetAllOutShipInfo(''); // 空参数
            // this.GetAllOutShipInfo('offline'); // offline参数
            // this.GetAllOutShipInfo('1'); // 数字参数
        },

        // 查询离线船舶
        GetAllOutShipInfo: function (type) {
            var _this = this;
            var url = global.IP + "/web/GetOutLineShipInfo?type=" + type;
            console.log('请求离线船舶数据，URL:', url);
            $.ajaxSettings.async = false;
            $.get(global.IP + "/web/GetOutLineShipInfo?type=" +type, function (data, status) {
                console.log('离线船舶数据请求成功，原始数据:', data);
                data = JSON.parse(data);
                _this.selectShipInfoList = [];
                if(data["2h"] != undefined) {
                    for (var i = 0; i < data["2h"].length; i++) {
                        var id = _this.$options.methods.ifnull.bind(this)(data["2h"][i].shipId);
                        var names = _this.$options.methods.ifnull.bind(this)(data["2h"][i].shipName);
                        var port = _this.$options.methods.ifnull.bind(this)(data["2h"][i].portName);
                        var owner = _this.$options.methods.ifnull.bind(this)(data["2h"][i].owner);
                        var phone = _this.$options.methods.ifnull.bind(this)(data["2h"][i].lxdh);
                        var bdId = _this.$options.methods.ifnull.bind(this)(data["2h"][i].bdId);
                        var mmsi = _this.$options.methods.ifnull.bind(this)(data["2h"][i].mmsi);
                        var captain = _this.$options.methods.ifnull.bind(this)(data["2h"][i].captain);
                        var outTime = "2小时";
                        var outLineReason = _this.$options.methods.ifnull.bind(this)(data["2h"][i].outLineReason);
                        outLineReason = (outLineReason == null || outLineReason == "null") ? "————":outLineReason;
                        owner = (owner == null||owner == "null")?"-":owner;
                        owner = owner == "-" ? captain : '-';
                        phone = (phone == null||phone == "null")?"-":phone;

                        _this.selectShipInfoList.push(
                            {
                                shipId: id,
                                shipName: names,
                                portName: port,
                                owner: owner,
                                shortOwner: owner.length > 3 ? owner.substring(0, 3) + "..." : owner,
                                phone: phone,
                                bdId: bdId,
                                mmsi: mmsi,
                                shortBdId: (bdId.length > 6) ? bdId.substring(0, 6) + "..." : bdId,
                                shortMMSI: (mmsi.length > 6) ? mmsi.substring(0, 6) + "..." : mmsi,
                                outTime: outTime,
                                outLineColor: 0,
                                outLineReason: outLineReason,
                            }
                        );
                    }
                }

                if(data["12h"] != undefined) {
                    for (var i = 0; i < data["12h"].length; i++) {
                        var id = _this.$options.methods.ifnull.bind(this)(data["12h"][i].shipId);
                        var names = _this.$options.methods.ifnull.bind(this)(data["12h"][i].shipName);
                        var port = _this.$options.methods.ifnull.bind(this)(data["12h"][i].portName);
                        var owner = _this.$options.methods.ifnull.bind(this)(data["12h"][i].owner);
                        var phone = _this.$options.methods.ifnull.bind(this)(data["12h"][i].lxdh);
                        var bdId = _this.$options.methods.ifnull.bind(this)(data["12h"][i].bdId);
                        var mmsi = _this.$options.methods.ifnull.bind(this)(data["12h"][i].mmsi);
                        var captain = _this.$options.methods.ifnull.bind(this)(data["12h"][i].captain);
                        var outTime = "12小时";
                        var outLineReason = _this.$options.methods.ifnull.bind(this)(data["12h"][i].outLineReason);
                        outLineReason = (outLineReason == null || outLineReason == "null") ? "————":outLineReason;

                        owner = (owner == null||owner == "null")?"-":owner;
                        owner = owner == "-" ? captain : '-';
                        phone = (phone == null||phone == "null")?"-":phone;
                        _this.selectShipInfoList.push(
                            {
                                shipId: id,
                                shipName: names,
                                portName: port,
                                owner: owner,
                                shortOwner: owner.length > 3 ? owner.substring(0, 3) + "..." : owner,
                                phone: phone,
                                bdId: bdId,
                                mmsi: mmsi,
                                shortBdId: (bdId.length > 6) ? bdId.substring(0, 6) + "..." : bdId,
                                shortMMSI: (mmsi.length > 6) ? mmsi.substring(0, 6) + "..." : mmsi,
                                outTime: outTime,
                                outLineColor: 0,
                                outLineReason: outLineReason,
                            }
                        );
                    }
                }

                if(data["24h"] != undefined) {
                    for (var i = 0; i < data["24h"].length; i++) {
                        var id = _this.$options.methods.ifnull.bind(this)(data["24h"][i].shipId);
                        var names = _this.$options.methods.ifnull.bind(this)(data["24h"][i].shipName);
                        var port = _this.$options.methods.ifnull.bind(this)(data["24h"][i].portName);
                        var owner = _this.$options.methods.ifnull.bind(this)(data["24h"][i].owner);
                        var phone = _this.$options.methods.ifnull.bind(this)(data["24h"][i].lxdh);
                        var bdId = _this.$options.methods.ifnull.bind(this)(data["24h"][i].bdId);
                        var mmsi = _this.$options.methods.ifnull.bind(this)(data["24h"][i].mmsi);
                        var captain = _this.$options.methods.ifnull.bind(this)(data["24h"][i].captain);
                        var outTime = "24小时";
                        var outLineReason = _this.$options.methods.ifnull.bind(this)(data["24h"][i].outLineReason);
                        outLineReason = (outLineReason == null || outLineReason == "null") ? "————":outLineReason;

                        owner = (owner == null||owner == "null")?"-":owner;
                        owner = owner == "-" ? captain : '-';
                        phone = (phone == null||phone == "null")?"-":phone;
                        _this.selectShipInfoList.push(
                            {
                                shipId: id,
                                shipName: names,
                                portName: port,
                                owner: owner,
                                shortOwner: owner.length > 3 ? owner.substring(0, 3) + "..." : owner,
                                phone: phone,
                                bdId: bdId,
                                mmsi: mmsi,
                                shortBdId: (bdId.length > 6) ? bdId.substring(0, 6) + "..." : bdId,
                                shortMMSI: (mmsi.length > 6) ? mmsi.substring(0, 6) + "..." : mmsi,
                                outTime: outTime,
                                outLineColor: 0,
                                outLineReason: outLineReason,
                            }
                        );
                    }
                }

                if(data["48h"] != undefined) {
                    for (var i = 0; i < data["48h"].length; i++) {
                        var id = _this.$options.methods.ifnull.bind(this)(data["48h"][i].shipId);
                        var names = _this.$options.methods.ifnull.bind(this)(data["48h"][i].shipName);
                        var port = _this.$options.methods.ifnull.bind(this)(data["48h"][i].portName);
                        var owner = _this.$options.methods.ifnull.bind(this)(data["48h"][i].owner);
                        var phone = _this.$options.methods.ifnull.bind(this)(data["48h"][i].lxdh);
                        var bdId = _this.$options.methods.ifnull.bind(this)(data["48h"][i].bdId);
                        var mmsi = _this.$options.methods.ifnull.bind(this)(data["48h"][i].mmsi);
                        var captain = _this.$options.methods.ifnull.bind(this)(data["48h"][i].captain);
                        var outTime = "48小时";
                        var outLineReason = _this.$options.methods.ifnull.bind(this)(data["48h"][i].outLineReason);
                        outLineReason = (outLineReason == null || outLineReason == "null") ? "————":outLineReason;

                        owner = (owner == null||owner == "null")?"-":owner;
                        owner = owner == "-" ? captain : '-';
                        phone = (phone == null||phone == "null")?"-":phone;
                        _this.selectShipInfoList.push(
                            {
                                shipId: id,
                                shipName: names,
                                portName: port,
                                owner: owner,
                                shortOwner: owner.length > 3 ? owner.substring(0, 3) + "..." : owner,
                                phone: phone,
                                bdId: bdId,
                                mmsi: mmsi,
                                shortBdId: (bdId.length > 6) ? bdId.substring(0, 6) + "..." : bdId,
                                shortMMSI: (mmsi.length > 6) ? mmsi.substring(0, 6) + "..." : mmsi,
                                outTime: outTime,
                                outLineColor: 0,
                                outLineReason: outLineReason,
                            }
                        );
                    }
                }

                 if(data["99h"] != undefined) {
                    for (var i = 0; i <data["99h"].length; i++) {
                        var id = _this.$options.methods.ifnull.bind(this)(data["99h"][i].shipId);
                        var names = _this.$options.methods.ifnull.bind(this)(data["99h"][i].shipName);
                        var port = _this.$options.methods.ifnull.bind(this)(data["99h"][i].portName);
                        var owner = _this.$options.methods.ifnull.bind(this)(data["99h"][i].owner);
                        var phone = _this.$options.methods.ifnull.bind(this)(data["99h"][i].lxdh);
                        var bdId = _this.$options.methods.ifnull.bind(this)(data["99h"][i].bdId);
                        var mmsi = _this.$options.methods.ifnull.bind(this)(data["99h"][i].mmsi);
                        var captain = _this.$options.methods.ifnull.bind(this)(data["99h"][i].captain);
                        var outTime = "48小时外";
                        var outLineReason = _this.$options.methods.ifnull.bind(this)(data["99h"][i].outLineReason);
                        outLineReason = (outLineReason == null || outLineReason == "null") ? "————":outLineReason;

                        owner = (owner == null||owner == "null")?"-":owner;
                        owner = owner == "-" ? captain : '-';
                        phone = (phone == null||phone == "null")?"-":phone;
                        _this.selectShipInfoList.push(
                            {
                                shipId: id,
                                shipName: names,
                                portName: port,
                                owner: owner,
                                shortOwner: owner.length > 3 ? owner.substring(0, 3) + "..." : owner,
                                phone: phone,
                                bdId: bdId,
                                mmsi: mmsi,
                                shortBdId: (bdId.length > 6) ? bdId.substring(0, 6) + "..." : bdId,
                                shortMMSI: (mmsi.length > 6) ? mmsi.substring(0, 6) + "..." : mmsi,
                                outTime: outTime,
                                outLineColor: 1,
                                outLineReason: outLineReason,
                            }
                        );
                    }
                }

                // 如果没有数据，保持空数组状态
                if (_this.selectShipInfoList.length === 0) {
                    console.log('API返回空数据，显示空数据状态');
                }

                _this.curSelecctShipCount = _this.selectShipInfoList.length;
                _this.mapShipShowOrNot = false;
                _this.portOrPo = true;
                _this.showOutLineShip = true;
                _this.ifFishArea = false;
                _this.ShipSearch1IsSow = true;
                _this.$options.methods.changeCurIndex.bind(this)("ShipSearch1");

                // 设置加载完成状态
                _this.loading = false;
                console.log('离线船舶数据加载完成，共', _this.selectShipInfoList.length, '条数据');
                console.log('处理后的船舶列表:', _this.selectShipInfoList);
            }).fail(function(xhr, status, error) {
                // 请求失败处理
                console.error('离线船舶数据请求失败:', error);
                _this.loading = false;
                _this.selectShipInfoList = [];
            });
            $.ajaxSettings.async = true;
        },

        // 加载统计信息
        loadStatistics() {
            // 这里可以添加加载统计信息的逻辑
            this.totalShipCount = this.shipList.length + this.offlineShipCount;
            console.log('加载统计信息');
        },
        
        // 处理关闭按钮点击
        handleClose() {
            this.$emit('close');
        },

        // 切换菜单显示
        toggleMenu() {
            this.menuVisible = !this.menuVisible;
        },

        // 返回上一页
        goBack() {
            this.$emit('close');
        },

        // 设置活动标签
        setActiveTab(tabId) {
            this.activeTab = tabId;
            this.menuVisible = false; // 移动端选择后关闭菜单

            // 根据选择的标签加载相应数据
            if (tabId === 'offline') {
                this.loadOfflineShipList();
            } else if (tabId === 'statistics') {
                this.loadStatistics();
            }
        },

        // 空值处理方法
        ifnull(value) {
            return value == null || value == undefined || value == "null" ? "" : value;
        },

        // 改变当前索引方法
        changeCurIndex(index) {
            // 这里可以添加改变当前索引的逻辑
            console.log('改变当前索引:', index);
        }

    }
}
</script>

<style scoped>
/* 确保全屏显示，覆盖所有可能的父容器限制 */
* {
    box-sizing: border-box;
}

/* 全屏覆盖层 */
.fullscreen-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: #f5f5f5;
    overflow: hidden;
}

/* 基础布局样式 - 全屏显示 */
.ship-detail {
    width: 100vw !important;
    height: 100vh !important;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 9999 !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden;
    max-width: none !important;
    max-height: none !important;
    min-width: 100vw !important;
    min-height: 100vh !important;
}

.mobile-menu-header {
    display: none;
    position: relative;
    background-color: #1890ff;
    height: 40px;
    align-items: center;
    justify-content: flex-end;
    padding: 8px 20px 8px 0;
}

@media (max-width: 768px) {
    .mobile-menu-header {
        display: flex;
    }
}

.menu-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: white;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    position: relative;
    right: 0;
}

.back-btn {
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
    width: 30px;
    height: 30px;
    padding: 0;
}

.back-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    border-top: 2px solid white;
    border-left: 2px solid white;
    transform: translate(-50%, -50%) rotate(-45deg);
}

.menu-btn:hover,
.back-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.detail-layout {
    display: flex;
    flex: 1;
    background-color: white;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* 左侧菜单样式 */
.menu-sidebar {
    width: 160px;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.sidebar-header {
    display: none;
    padding: 15px;
    background-color: #1890ff;
    color: white;
    justify-content: space-between;
    align-items: center;
}

.close-menu-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
}

.menu-item {
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s;
    font-size: 14px;
}

.menu-item:hover {
    background-color: #e9ecef;
}

.menu-item.active {
    background-color: #1890ff;
    color: white;
}

/* 移动端菜单样式 */
@media (max-width: 768px) {
    .menu-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 1001;
        transform: translateX(-100%);
        width: 250px;
    }

    .menu-sidebar.mobile-visible {
        transform: translateX(0);
    }

    .sidebar-header {
        display: flex;
    }
}

/* 右侧内容区域 */
.content-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: white;
    width: 100%;
}

.info-section {
    display: flex;
    flex-direction: column;
}

.info-section h2 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 20px;
    font-weight: bold;
    border-bottom: 2px solid #1890ff;
    padding-bottom: 10px;
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.toolbar-left {
    display: flex;
    gap: 10px;
}

.tool-btn {
    padding: 6px 12px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.tool-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.tool-btn.active {
    background-color: #1890ff;
    border-color: #1890ff;
    color: white;
}

.count-display {
    font-size: 14px;
    color: #666;
    font-weight: bold;
}

/* 竖向表格样式 */
.vertical-table-container {
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.loading-message, .empty-message {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: #666;
    font-size: 14px;
}

/* 在线船舶 - 保持原有的垂直表格样式 */
.vertical-table {
    display: flex;
    overflow: hidden;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

/* 离线船舶 - 水平表格样式 */
.offline-horizontal-table {
    overflow: hidden;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    max-height: 600px;
    overflow-y: auto; /* 垂直滚动 */
}

.offline-ship-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.offline-ship-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 12px 8px;
    text-align: center;
    border-bottom: 2px solid #dee2e6;
    border-right: 1px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
    white-space: normal; /* 允许表头换行 */
    word-wrap: break-word; /* 长单词换行 */
    vertical-align: middle; /* 垂直居中 */
}

.offline-ship-table th:last-child {
    border-right: none;
}

.offline-ship-table td {
    padding: 12px 8px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    border-right: 1px solid #dee2e6;
    color: #495057;
    background-color: white;
    white-space: normal; /* 允许换行 */
    word-wrap: break-word; /* 长单词换行 */
    vertical-align: middle; /* 垂直居中 */
}

.offline-ship-table td:last-child {
    border-right: none;
}

.offline-ship-row {
    cursor: pointer;
    transition: background-color 0.2s;
}

.offline-ship-row:hover {
    background-color: #f0f8ff;
}

.offline-ship-row:hover td {
    background-color: #f0f8ff;
}

/* 船名单元格特殊样式 */
.ship-name-cell {
    color: #007bff !important;
    font-weight: bold;
    cursor: pointer;
}

.ship-name-cell:hover {
    color: #0056b3 !important;
    text-decoration: underline;
}

/* 离线时间单元格特殊样式 - 用于48小时外的船舶 */
.offline-time-cell {
    background-color: #dc3545 !important;
    color: white !important;
    font-weight: bold;
}

/* 离线原因列样式 - 自适应内容高度，允许换行 */
.reason-cell {
    white-space: normal !important; /* 允许换行 */
    word-wrap: break-word !important; /* 长单词换行 */
    text-align: center !important; /* 居中对齐 */
    max-width: 150px; /* 限制最大宽度 */
    vertical-align: middle; /* 垂直居中 */
}

/* 空数据占位行样式 */
.empty-row td {
    color: #999 !important; /* 灰色文字 */
    font-style: italic; /* 斜体 */
    background-color: #f9f9f9 !important; /* 浅灰色背景 */
}

.empty-row:hover td {
    background-color: #f9f9f9 !important; /* 悬停时保持浅灰色背景 */
    cursor: default; /* 默认鼠标样式，不显示点击手势 */
}

/* 左侧表头列 */
.table-headers {
    width: 120px;
    min-width: 120px;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
}

.header-cell {
    padding: 12px 8px;
    font-weight: bold;
    font-size: 13px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    color: #495057;
    min-height: 40px;
    display: flex;
    align-items: flex-start; /* 内容顶部对齐 */
    justify-content: center;
    background-color: #f8f9fa;
}



.header-cell:last-child {
    border-bottom: none;
}

/* 右侧数据区域 */
.table-data-container {
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
}

.table-data {
    display: flex;
    height: 100%;
    min-width: fit-content;
}

/* 每一列数据 */
.data-column {
    min-width: 120px;
    width: auto; /* 自动宽度 */
    border-right: 1px solid #dee2e6;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    flex-direction: column;
}

.data-column:hover {
    background-color: #f0f8ff;
}

.data-column:last-child {
    border-right: none;
}

/* 数据单元格 */
.data-cell {
    padding: 12px 8px;
    font-size: 12px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    color: #495057;
    min-height: 40px;
    display: flex;
    align-items: flex-start; /* 内容顶部对齐 */
    justify-content: center;
    word-break: break-all;
    background-color: white;
}



.data-cell:last-child {
    border-bottom: none;
}

/* 船名单元格特殊样式 */
.ship-name-cell {
    font-weight: bold;
    color: #1890ff;
    background-color: #f0f8ff;
}

/* 离线时间单元格特殊样式 - 用于48小时外的船舶 */
.offline-time-cell {
    background-color: #dc3545 !important;
    color: white !important;
    font-weight: bold;
}



/* 统计信息网格样式 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.info-item .label {
    font-weight: bold;
    color: #495057;
}

.info-item .value {
    color: #1890ff;
    font-weight: bold;
}

.main-content {
    flex: 1;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .content-area {
        padding: 10px;
    }

    .toolbar {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .toolbar-left {
        justify-content: center;
    }

    .vertical-table-container {
        padding: 5px 0;
    }

    .table-headers {
        width: 100px;
        min-width: 100px;
    }

    .header-cell {
        padding: 8px 4px;
        font-size: 11px;
        min-height: 32px;
    }

    .data-column {
        min-width: 100px;
        width: 100px;
    }

    .data-cell {
        padding: 8px 4px;
        font-size: 11px;
        min-height: 32px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    /* 移动端在线船舶表格样式 */
    .vertical-table-container {
        padding: 5px 0;
    }

    .table-headers {
        width: 100px;
        min-width: 100px;
    }

    .header-cell {
        padding: 8px 4px;
        font-size: 11px;
        min-height: 32px;
    }

    .data-column {
        min-width: 100px;
        width: 100px;
    }

    .data-cell {
        padding: 8px 4px;
        font-size: 11px;
        min-height: 32px;
    }

    /* 移动端离线船舶表格样式 */
    .offline-horizontal-table {
        font-size: 10px;
    }

    .offline-ship-table th {
        padding: 6px 4px;
        font-size: 10px;
        white-space: normal;
        word-wrap: break-word;
        vertical-align: middle;
    }

    .offline-ship-table td {
        padding: 6px 4px;
        font-size: 10px;
        white-space: normal;
        word-wrap: break-word;
        vertical-align: middle;
    }

    .reason-cell {
        max-width: 100px;
        font-size: 9px;
    }

    /* 移动端空数据占位行样式 */
    .empty-row td {
        color: #999 !important;
        font-style: italic;
        background-color: #f9f9f9 !important;
        font-size: 10px;
    }
}

/* 桌面端隐藏移动端菜单 */
@media (min-width: 769px) {
    .mobile-menu-header {
        display: none !important;
    }
}
</style>
