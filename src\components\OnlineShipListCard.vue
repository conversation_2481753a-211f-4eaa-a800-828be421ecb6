<template>
    <div class="ship-detail fullscreen-overlay">
        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-header">
            <button @click="toggleMenu" class="menu-btn">☰</button>
            <button @click="goBack" class="back-btn"></button>
        </div>

        <!-- 整体布局：左侧菜单 + 右侧内容 -->
        <div class="detail-layout">
            <!-- 左侧菜单（移动端时变为侧滑抽屉） -->
            <div class="menu-sidebar" :class="{ 'mobile-visible': menuVisible }">
                <div class="sidebar-header">
                    <span>菜单</span>
                    <button @click="toggleMenu" class="close-menu-btn">×</button>
                </div>
                <div v-for="tab in tabs" :key="tab.id" :class="{ 'menu-item': true, active: activeTab === tab.id }"
                    @click="setActiveTab(tab.id)">
                    {{ tab.name }}
                </div>
            </div>

            <!-- 右侧详情内容区域 -->
            <div class="content-area">
                <!-- 在线船舶列表 -->
                <div v-if="activeTab === 'online'" class="info-section">
                    <h2>沪籍在线船舶/渔船总数</h2>

                    <!-- 操作栏 -->
                    <div class="toolbar">
                        <div class="toolbar-left">
                            <button class="tool-btn active">导出</button>
                            <button class="tool-btn">北斗通信</button>
                            <button class="tool-btn">显示离线船舶</button>
                        </div>
                        <div class="toolbar-right">
                            <span class="count-display">总数: {{ shipList.length }}</span>
                        </div>
                    </div>

                    <!-- 竖向表格容器 -->
                    <div class="vertical-table-container">
                        <div v-if="loading" class="loading-message">
                            <span>加载中...</span>
                        </div>

                        <div v-else-if="shipList.length === 0" class="empty-message">
                            <span>暂无数据</span>
                        </div>

                        <div v-else class="vertical-table">
                            <!-- 表头列（左侧垂直） -->
                            <div class="table-headers">
                                <div class="header-cell">序号</div>
                                <div class="header-cell">船名</div>
                                <div class="header-cell">船舶类型</div>
                                <div class="header-cell">组织机构</div>
                                <div class="header-cell">呼号</div>
                                <div class="header-cell">船长</div>
                                <div class="header-cell">型宽</div>
                                <div class="header-cell">作业场所</div>
                                <div class="header-cell">属地</div>
                                <div class="header-cell">作业类型</div>
                                <div class="header-cell">船舶所有人</div>
                                <div class="header-cell">功率</div>
                                <div class="header-cell">吨位</div>
                                <div class="header-cell">建造完工日期</div>
                                <div class="header-cell">船体材料</div>
                                <div class="header-cell">联系电话</div>
                                <div class="header-cell">北斗号</div>
                                <div class="header-cell">MMSI</div>
                                <div class="header-cell">离线原因</div>
                            </div>

                            <!-- 数据区域（右侧横向滚动） -->
                            <div class="table-data-container">
                                <div class="table-data">
                                    <!-- 每一列代表一条船舶数据 -->
                                    <div v-for="(ship, index) in shipList"
                                         :key="ship.id || index"
                                         class="data-column"
                                         @click="handleShipClick(ship)">
                                        <div class="data-cell">{{ index + 1 }}</div>
                                        <div class="data-cell ship-name-cell">{{ ship.shipName || '-' }}</div>
                                        <div class="data-cell">{{ ship.shipType || '国内捕捞船' }}</div>
                                        <div class="data-cell">{{ ship.organization || '崇明区' }}</div>
                                        <div class="data-cell">{{ ship.callSign || '-' }}</div>
                                        <div class="data-cell">{{ ship.length || '32.99' }}米</div>
                                        <div class="data-cell">{{ ship.width || '6.3' }}米</div>
                                        <div class="data-cell">{{ ship.operationArea || 'C2渔区' }}</div>
                                        <div class="data-cell">{{ ship.location || '崇明区' }}</div>
                                        <div class="data-cell">{{ ship.operationType || '拖网' }}</div>
                                        <div class="data-cell">{{ ship.owner || '-' }}</div>
                                        <div class="data-cell">{{ ship.power || '232' }}千瓦</div>
                                        <div class="data-cell">{{ ship.tonnage || '185' }}吨</div>
                                        <div class="data-cell">{{ ship.completionDate || '2015-06-24' }}</div>
                                        <div class="data-cell">{{ ship.hullMaterial || '钢质' }}</div>
                                        <div class="data-cell">{{ ship.phone || '-' }}</div>
                                        <div class="data-cell">{{ ship.beidouNo || '-' }}</div>
                                        <div class="data-cell">{{ ship.mmsi || '-' }}</div>
                                        <div class="data-cell">{{ ship.offlineReason || '-' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 离线船舶列表 -->
                <div v-if="activeTab === 'offline'" class="info-section">
                    <h2>离线船舶列表</h2>

                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="toolbar-left">
                            <!-- 时间范围选择器 -->
                            <div class="time-range-selector">
                                <label for="timeRange">离线时间：</label>
                                <select id="timeRange" v-model="selectedTimeRange" @change="onTimeRangeChange" class="time-range-select">
                                    <option value="0">全部时间段</option>
                                    <option value="2">2小时内离线</option>
                                    <option value="12">12小时内离线</option>
                                    <option value="24">24小时内离线</option>
                                    <option value="48">48小时内离线</option>
                                    <option value="99">超过72小时离线</option>
                                </select>
                            </div>
                            <button class="tool-btn active">导出</button>
                            <button class="tool-btn">北斗通信</button>
                        </div>
                        <div class="toolbar-right">
                            <span class="count-display">总数: {{ selectShipInfoList.length }}</span>
                        </div>
                    </div>

                    <!-- 竖向表格容器 -->
                    <div class="vertical-table-container">
                        <div v-if="loading" class="loading-message">
                            <span>加载中...</span>
                        </div>

                        <div v-else class="offline-horizontal-table">
                            <!-- 传统表格布局 -->
                            <table class="offline-ship-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>船舶名称</th>
                                        <th>离线时间</th>
                                        <th>船舶所有人</th>
                                        <th>联系电话</th>
                                        <th>北斗号</th>
                                        <th>MMSI</th>
                                        <th>离线原因</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 有数据时显示船舶列表 -->
                                    <tr v-for="(ship, index) in selectShipInfoList"
                                        :key="ship.shipId || index"
                                        class="offline-ship-row"
                                        @click="handleShipClick(ship)">
                                        <td>{{ index + 1 }}</td>
                                        <td class="ship-name-cell">{{ ship.shipName || '-' }}</td>
                                        <td :class="{'offline-time-cell': ship.outLineColor === 1}">{{ ship.outTime || '-' }}</td>
                                        <td>{{ ship.owner || '-' }}</td>
                                        <td>{{ ship.phone || '-' }}</td>
                                        <td class="bdid-cell">{{ ship.bdId || '-' }}</td>
                                        <td>{{ ship.mmsi || '-' }}</td>
                                        <td class="reason-cell">{{ ship.outLineReason || '-' }}</td>
                                    </tr>
                                    <!-- 无数据时显示占位行 -->
                                    <tr v-if="selectShipInfoList.length === 0" class="empty-row">
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div v-if="activeTab === 'statistics'" class="info-section">
                    <h2>船舶统计信息</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">在线船舶数：</span>
                            <span class="value">{{ shipList.length }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">离线船舶数：</span>
                            <span class="value">{{ offlineShipCount }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">总船舶数：</span>
                            <span class="value">{{ totalShipCount }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">在线率：</span>
                            <span class="value">{{ onlineRate }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import global from './Global.vue';

export default {
    name: 'OnlineShipListCard',
    data() {
        return {
            shipList: [
                {
                    id: 1,
                    shipName: '沪崇渔11003',
                    portName: '吴家港',
                    owner: '尹建飞',
                    phone: '13816807592',
                    beidouNo: '203131...',
                    mmsi: '412370392',
                    offlineReason: '-'
                },
                {
                    id: 2,
                    shipName: '沪崇渔11005',
                    portName: '吴家港',
                    owner: '张秀林',
                    phone: '13040664848',
                    beidouNo: '392748...',
                    mmsi: '412370304',
                    offlineReason: '-'
                },
                {
                    id: 3,
                    shipName: '沪崇渔11006',
                    portName: '吴家港',
                    owner: '张秀林',
                    phone: '13040664848',
                    beidouNo: '397128...',
                    mmsi: '412370317',
                    offlineReason: '-'
                }
            ],
            loading: false,
            menuVisible: false,
            activeTab: 'online',
            offlineShipCount: 0,
            totalShipCount: 0,
            // 离线船舶相关数据属性
            selectShipInfoList: [], // 离线船舶列表数据
            curSelecctShipCount: 0, // 当前选中船舶数量
            mapShipShowOrNot: false, // 地图船舶显示状态
            portOrPo: true, // 港口或位置显示状态
            showOutLineShip: true, // 显示离线船舶状态
            ifFishArea: false, // 是否渔区状态
            ShipSearch1IsSow: true, // 船舶搜索1显示状态
            selectedTimeRange: "0", // 选中的时间范围，默认为全部时间段
            tabs: [
                { id: 'online', name: '在线船舶' },
                { id: 'offline', name: '离线船舶' },
                { id: 'statistics', name: '统计信息' }
            ]
        }
    },

    computed: {
        onlineRate() {
            if (this.totalShipCount === 0) return 0;
            return Math.round((this.shipList.length / this.totalShipCount) * 100);
        }
    },

    mounted() {
        this.loadShipList();
    },

    methods: {
        // 加载船舶列表数据
        loadShipList() {
            const _this = this;
            // _this.loading = true;

            // // 这里调用获取在线船舶列表的API
            // $.get(global.IP + "/web/GetOnlineShipList", function (data) {
            //     _this.shipList = data.map((ship, index) => ({
            //         id: ship.id || index,
            //         shipName: ship.shipName,
            //         portName: ship.portName,
            //         owner: ship.owner,
            //         phone: ship.phone,
            //         beidouNo: ship.beidouNo,
            //         mmsi: ship.mmsi,
            //         offlineReason: ship.offlineReason || '-'
            //     }));

            //     console.log('在线船舶列表已加载:', _this.shipList);
            // }).fail(function (error) {
            //     console.error('获取在线船舶列表失败:', error);
            // }).always(() => {
            //     _this.loading = false;
            // });
        },

        // 加载离线船舶列表
        loadOfflineShipList() {
            console.log('开始加载离线船舶列表...');
            this.loading = true; // 设置加载状态

            // 根据选择的时间范围加载数据
            this.loadOfflineShipsByTimeRange();
        },

        // 时间范围选择变化处理
        onTimeRangeChange() {
            console.log(`时间范围切换为: ${this.getTimeRangeDescription(this.selectedTimeRange)}`);
            this.loading = true;
            this.loadOfflineShipsByTimeRange();
        },

        // 根据时间范围加载离线船舶数据
        async loadOfflineShipsByTimeRange() {
            try {
                // 初始化船舶列表
                this.selectShipInfoList = [];

                // 根据选择的时间范围获取数据
                const shipCount = await this.GetAllOutShipInfoAsync(this.selectedTimeRange);

                // 显示查询结果
                const timeDesc = this.getTimeRangeDescription(this.selectedTimeRange);
                console.log(`${timeDesc}查询完成，共找到 ${shipCount} 条数据`);

                if (shipCount > 0) {
                    console.log('离线船舶数据加载完成');
                    this.curSelecctShipCount = this.selectShipInfoList.length;
                    this.mapShipShowOrNot = false;
                    this.portOrPo = true;
                    this.showOutLineShip = true;
                    this.ifFishArea = false;
                    this.ShipSearch1IsSow = true;
                    this.$options.methods.changeCurIndex.bind(this)("ShipSearch1");
                } else {
                    console.log(`当前${timeDesc}没有离线船舶`);
                }

                this.loading = false;
            } catch (error) {
                console.error(`获取离线船舶数据失败:`, error);
                this.loading = false;
            }
        },

        // 获取离线船舶数据
        async tryMultipleParams() {
            // 使用 type="0" 获取所有时间段的离线船舶信息
            // 根据后端定义：type="0" 返回所有时间段的离线船舶信息（2h, 12h, 24h, 48h, 99h）
            let totalShips = 0;

            // 初始化船舶列表
            this.selectShipInfoList = [];

            try {
                // 直接使用 type="0" 获取所有离线船舶
                totalShips = await this.GetAllOutShipInfoAsync("0");

                // 如果获取到数据，记录信息
                if (totalShips > 0) {
                    console.log(`发现 ${totalShips} 条离线船舶数据`);
                }
            } catch (error) {
                console.error(`获取离线船舶数据失败:`, error);
            }

            // 显示查询结果
            console.log(`离线船舶查询完成，共找到 ${totalShips} 条数据`);

            if (totalShips > 0) {
                console.log('离线船舶数据加载完成');
                this.curSelecctShipCount = this.selectShipInfoList.length;
                this.mapShipShowOrNot = false;
                this.portOrPo = true;
                this.showOutLineShip = true;
                this.ifFishArea = false;
                this.ShipSearch1IsSow = true;
                this.$options.methods.changeCurIndex.bind(this)("ShipSearch1");
            } else {
                console.log('当前没有离线船舶，所有船舶状态良好');
            }

            this.loading = false;
        },

        // 异步版本的查询离线船舶方法
        GetAllOutShipInfoAsync(type) {
            return new Promise((resolve, reject) => {
                const _this = this;
                const url = global.IP + "/web/GetOutLineShipInfo?type=" + type;

                $.get(url)
                    .done(function(data) {
                        try {
                            if (typeof data === 'string') {
                                data = JSON.parse(data);
                            }

                            let shipCount = 0;

                            // 根据后端定义，处理所有时间段的数据：2h, 12h, 24h, 48h, 99h
                            const timeRanges = ['2h', '12h', '24h', '48h', '99h'];

                            for (const timeRange of timeRanges) {
                                if (data[timeRange] && Array.isArray(data[timeRange]) && data[timeRange].length > 0) {
                                    // 为每个船舶数据添加时间段信息
                                    const shipsWithTimeRange = data[timeRange].map(ship => ({
                                        ...ship,
                                        outTime: _this.getTimeRangeDescription(timeRange)
                                    }));
                                    _this.processShipData(shipsWithTimeRange);
                                    shipCount += data[timeRange].length;
                                    console.log(`处理 ${timeRange} 数据: ${data[timeRange].length} 条`);
                                }
                            }

                            // 如果没有按时间段分组的数据，检查是否是直接的数组格式
                            if (shipCount === 0 && Array.isArray(data) && data.length > 0) {
                                _this.processShipData(data);
                                shipCount += data.length;
                                console.log(`处理直接数组数据: ${data.length} 条`);
                            }

                            // 如果这是第一次获取到数据，设置相关状态
                            if (shipCount > 0 && _this.selectShipInfoList.length === shipCount) {
                                _this.curSelecctShipCount = _this.selectShipInfoList.length;
                                _this.mapShipShowOrNot = false;
                                _this.portOrPo = true;
                                _this.showOutLineShip = true;
                                _this.ifFishArea = false;
                                _this.ShipSearch1IsSow = true;
                                _this.$options.methods.changeCurIndex.bind(_this)("ShipSearch1");
                            }

                            resolve(shipCount);
                        } catch (error) {
                            reject(error);
                        }
                    })
                    .fail(function(xhr, status, error) {
                        reject(error);
                    });
            });
        },

        // 获取时间段描述
        getTimeRangeDescription(timeRange) {
            // 处理选择器的值（字符串格式）
            switch(timeRange) {
                case "0":
                    return "全部时间段";
                case "2":
                    return "2小时内";
                case "12":
                    return "12小时内";
                case "24":
                    return "24小时内";
                case "48":
                    return "48小时内";
                case "99":
                    return "超过72小时";
                // 处理API返回的时间段格式
                case "2h":
                    return "2小时内";
                case "12h":
                    return "12小时内";
                case "24h":
                    return "24小时内";
                case "48h":
                    return "48小时内";
                case "99h":
                    return "超过72小时";
                default:
                    return "未知";
            }
        },

        // 处理船舶数据的通用方法
        processShipData(shipArray) {
            const _this = this;
            for (var i = 0; i < shipArray.length; i++) {
                var shipData = shipArray[i];
                var id = _this.$options.methods.ifnull.bind(this)(shipData.shipId);
                var names = _this.$options.methods.ifnull.bind(this)(shipData.shipName);
                var port = _this.$options.methods.ifnull.bind(this)(shipData.portName);
                var owner = _this.$options.methods.ifnull.bind(this)(shipData.owner);
                var phone = _this.$options.methods.ifnull.bind(this)(shipData.phone);
                var bdId = _this.$options.methods.ifnull.bind(this)(shipData.bdId);
                var mmsi = _this.$options.methods.ifnull.bind(this)(shipData.mmsi);
                var outTime = _this.$options.methods.ifnull.bind(this)(shipData.outTime);
                var outLineReason = _this.$options.methods.ifnull.bind(this)(shipData.outLineReason);

                // 处理离线时间和颜色
                var outLineColor = 0;
                if (outTime && outTime.includes("48小时外")) {
                    outLineColor = 1;
                }

                // 处理短格式显示（保留船东和MMSI的短格式，北斗号显示完整）
                var shortOwner = owner.length > 6 ? owner.substring(0, 6) + "..." : owner;
                var shortMMSI = mmsi.length > 6 ? mmsi.substring(0, 6) + "..." : mmsi;

                _this.selectShipInfoList.push({
                    shipId: id,
                    shipName: names,
                    portName: port,
                    owner: owner,
                    phone: phone,
                    bdId: bdId, // 北斗号始终显示完整
                    mmsi: mmsi,
                    outTime: outTime,
                    outLineColor: outLineColor,
                    outLineReason: outLineReason,
                    shortOwner: shortOwner,
                    shortMMSI: shortMMSI
                });
            }
        },

        // 查询离线船舶（保留原方法作为备用）
        GetAllOutShipInfo: function (type) {
            var _this = this;
            $.ajaxSettings.async = false;
            $.get(global.IP + "/web/GetOutLineShipInfo?type=" +type, function (data, status) {
                data = JSON.parse(data);
                _this.selectShipInfoList = [];
                if(data["2h"] != undefined) {
                    for (var i = 0; i < data["2h"].length; i++) {
                        var id = _this.$options.methods.ifnull.bind(this)(data["2h"][i].shipId);
                        var names = _this.$options.methods.ifnull.bind(this)(data["2h"][i].shipName);
                        var port = _this.$options.methods.ifnull.bind(this)(data["2h"][i].portName);
                        var owner = _this.$options.methods.ifnull.bind(this)(data["2h"][i].owner);
                        var phone = _this.$options.methods.ifnull.bind(this)(data["2h"][i].lxdh);
                        var bdId = _this.$options.methods.ifnull.bind(this)(data["2h"][i].bdId);
                        var mmsi = _this.$options.methods.ifnull.bind(this)(data["2h"][i].mmsi);
                        var captain = _this.$options.methods.ifnull.bind(this)(data["2h"][i].captain);
                        var outTime = "2小时";
                        var outLineReason = _this.$options.methods.ifnull.bind(this)(data["2h"][i].outLineReason);
                        outLineReason = (outLineReason == null || outLineReason == "null") ? "————":outLineReason;
                        owner = (owner == null||owner == "null")?"-":owner;
                        owner = owner == "-" ? captain : '-';
                        phone = (phone == null||phone == "null")?"-":phone;

                        _this.selectShipInfoList.push(
                            {
                                shipId: id,
                                shipName: names,
                                portName: port,
                                owner: owner,
                                shortOwner: owner.length > 3 ? owner.substring(0, 3) + "..." : owner,
                                phone: phone,
                                bdId: bdId,
                                mmsi: mmsi,

                                shortMMSI: (mmsi.length > 6) ? mmsi.substring(0, 6) + "..." : mmsi,
                                outTime: outTime,
                                outLineColor: 0,
                                outLineReason: outLineReason,
                            }
                        );
                    }
                }

                if(data["12h"] != undefined) {
                    for (var i = 0; i < data["12h"].length; i++) {
                        var id = _this.$options.methods.ifnull.bind(this)(data["12h"][i].shipId);
                        var names = _this.$options.methods.ifnull.bind(this)(data["12h"][i].shipName);
                        var port = _this.$options.methods.ifnull.bind(this)(data["12h"][i].portName);
                        var owner = _this.$options.methods.ifnull.bind(this)(data["12h"][i].owner);
                        var phone = _this.$options.methods.ifnull.bind(this)(data["12h"][i].lxdh);
                        var bdId = _this.$options.methods.ifnull.bind(this)(data["12h"][i].bdId);
                        var mmsi = _this.$options.methods.ifnull.bind(this)(data["12h"][i].mmsi);
                        var captain = _this.$options.methods.ifnull.bind(this)(data["12h"][i].captain);
                        var outTime = "12小时";
                        var outLineReason = _this.$options.methods.ifnull.bind(this)(data["12h"][i].outLineReason);
                        outLineReason = (outLineReason == null || outLineReason == "null") ? "————":outLineReason;

                        owner = (owner == null||owner == "null")?"-":owner;
                        owner = owner == "-" ? captain : '-';
                        phone = (phone == null||phone == "null")?"-":phone;
                        _this.selectShipInfoList.push(
                            {
                                shipId: id,
                                shipName: names,
                                portName: port,
                                owner: owner,
                                shortOwner: owner.length > 3 ? owner.substring(0, 3) + "..." : owner,
                                phone: phone,
                                bdId: bdId,
                                mmsi: mmsi,

                                shortMMSI: (mmsi.length > 6) ? mmsi.substring(0, 6) + "..." : mmsi,
                                outTime: outTime,
                                outLineColor: 0,
                                outLineReason: outLineReason,
                            }
                        );
                    }
                }

                if(data["24h"] != undefined) {
                    for (var i = 0; i < data["24h"].length; i++) {
                        var id = _this.$options.methods.ifnull.bind(this)(data["24h"][i].shipId);
                        var names = _this.$options.methods.ifnull.bind(this)(data["24h"][i].shipName);
                        var port = _this.$options.methods.ifnull.bind(this)(data["24h"][i].portName);
                        var owner = _this.$options.methods.ifnull.bind(this)(data["24h"][i].owner);
                        var phone = _this.$options.methods.ifnull.bind(this)(data["24h"][i].lxdh);
                        var bdId = _this.$options.methods.ifnull.bind(this)(data["24h"][i].bdId);
                        var mmsi = _this.$options.methods.ifnull.bind(this)(data["24h"][i].mmsi);
                        var captain = _this.$options.methods.ifnull.bind(this)(data["24h"][i].captain);
                        var outTime = "24小时";
                        var outLineReason = _this.$options.methods.ifnull.bind(this)(data["24h"][i].outLineReason);
                        outLineReason = (outLineReason == null || outLineReason == "null") ? "————":outLineReason;

                        owner = (owner == null||owner == "null")?"-":owner;
                        owner = owner == "-" ? captain : '-';
                        phone = (phone == null||phone == "null")?"-":phone;
                        _this.selectShipInfoList.push(
                            {
                                shipId: id,
                                shipName: names,
                                portName: port,
                                owner: owner,
                                shortOwner: owner.length > 3 ? owner.substring(0, 3) + "..." : owner,
                                phone: phone,
                                bdId: bdId,
                                mmsi: mmsi,

                                shortMMSI: (mmsi.length > 6) ? mmsi.substring(0, 6) + "..." : mmsi,
                                outTime: outTime,
                                outLineColor: 0,
                                outLineReason: outLineReason,
                            }
                        );
                    }
                }

                if(data["48h"] != undefined) {
                    for (var i = 0; i < data["48h"].length; i++) {
                        var id = _this.$options.methods.ifnull.bind(this)(data["48h"][i].shipId);
                        var names = _this.$options.methods.ifnull.bind(this)(data["48h"][i].shipName);
                        var port = _this.$options.methods.ifnull.bind(this)(data["48h"][i].portName);
                        var owner = _this.$options.methods.ifnull.bind(this)(data["48h"][i].owner);
                        var phone = _this.$options.methods.ifnull.bind(this)(data["48h"][i].lxdh);
                        var bdId = _this.$options.methods.ifnull.bind(this)(data["48h"][i].bdId);
                        var mmsi = _this.$options.methods.ifnull.bind(this)(data["48h"][i].mmsi);
                        var captain = _this.$options.methods.ifnull.bind(this)(data["48h"][i].captain);
                        var outTime = "48小时";
                        var outLineReason = _this.$options.methods.ifnull.bind(this)(data["48h"][i].outLineReason);
                        outLineReason = (outLineReason == null || outLineReason == "null") ? "————":outLineReason;

                        owner = (owner == null||owner == "null")?"-":owner;
                        owner = owner == "-" ? captain : '-';
                        phone = (phone == null||phone == "null")?"-":phone;
                        _this.selectShipInfoList.push(
                            {
                                shipId: id,
                                shipName: names,
                                portName: port,
                                owner: owner,
                                shortOwner: owner.length > 3 ? owner.substring(0, 3) + "..." : owner,
                                phone: phone,
                                bdId: bdId,
                                mmsi: mmsi,

                                shortMMSI: (mmsi.length > 6) ? mmsi.substring(0, 6) + "..." : mmsi,
                                outTime: outTime,
                                outLineColor: 0,
                                outLineReason: outLineReason,
                            }
                        );
                    }
                }

                 if(data["99h"] != undefined) {
                    for (var i = 0; i <data["99h"].length; i++) {
                        var id = _this.$options.methods.ifnull.bind(this)(data["99h"][i].shipId);
                        var names = _this.$options.methods.ifnull.bind(this)(data["99h"][i].shipName);
                        var port = _this.$options.methods.ifnull.bind(this)(data["99h"][i].portName);
                        var owner = _this.$options.methods.ifnull.bind(this)(data["99h"][i].owner);
                        var phone = _this.$options.methods.ifnull.bind(this)(data["99h"][i].lxdh);
                        var bdId = _this.$options.methods.ifnull.bind(this)(data["99h"][i].bdId);
                        var mmsi = _this.$options.methods.ifnull.bind(this)(data["99h"][i].mmsi);
                        var captain = _this.$options.methods.ifnull.bind(this)(data["99h"][i].captain);
                        var outTime = "48小时外";
                        var outLineReason = _this.$options.methods.ifnull.bind(this)(data["99h"][i].outLineReason);
                        outLineReason = (outLineReason == null || outLineReason == "null") ? "————":outLineReason;

                        owner = (owner == null||owner == "null")?"-":owner;
                        owner = owner == "-" ? captain : '-';
                        phone = (phone == null||phone == "null")?"-":phone;
                        _this.selectShipInfoList.push(
                            {
                                shipId: id,
                                shipName: names,
                                portName: port,
                                owner: owner,
                                shortOwner: owner.length > 3 ? owner.substring(0, 3) + "..." : owner,
                                phone: phone,
                                bdId: bdId,
                                mmsi: mmsi,

                                shortMMSI: (mmsi.length > 6) ? mmsi.substring(0, 6) + "..." : mmsi,
                                outTime: outTime,
                                outLineColor: 1,
                                outLineReason: outLineReason,
                            }
                        );
                    }
                }

                // 如果没有数据，保持空数组状态
                if (_this.selectShipInfoList.length === 0) {
                    console.log('API返回空数据，显示空数据状态');
                }

                _this.curSelecctShipCount = _this.selectShipInfoList.length;
                _this.mapShipShowOrNot = false;
                _this.portOrPo = true;
                _this.showOutLineShip = true;
                _this.ifFishArea = false;
                _this.ShipSearch1IsSow = true;
                _this.$options.methods.changeCurIndex.bind(this)("ShipSearch1");

                // 设置加载完成状态
                _this.loading = false;
            }).fail(function(xhr, status, error) {
                // 请求失败处理
                _this.loading = false;
                _this.selectShipInfoList = [];
            });
            $.ajaxSettings.async = true;
        },

        // 加载统计信息
        loadStatistics() {
            // 这里可以添加加载统计信息的逻辑
            this.totalShipCount = this.shipList.length + this.offlineShipCount;
            console.log('加载统计信息');
        },
        
        // 处理关闭按钮点击
        handleClose() {
            this.$emit('close');
        },

        // 切换菜单显示
        toggleMenu() {
            this.menuVisible = !this.menuVisible;
        },

        // 返回上一页
        goBack() {
            this.$emit('close');
        },

        // 设置活动标签
        setActiveTab(tabId) {
            this.activeTab = tabId;
            this.menuVisible = false; // 移动端选择后关闭菜单

            // 根据选择的标签加载相应数据
            if (tabId === 'offline') {
                this.loadOfflineShipList();
            } else if (tabId === 'statistics') {
                this.loadStatistics();
            }
        },

        // 空值处理方法
        ifnull(value) {
            return value == null || value == undefined || value == "null" ? "" : value;
        },

        // 改变当前索引方法
        changeCurIndex(index) {
            // 这里可以添加改变当前索引的逻辑
            console.log('改变当前索引:', index);
        }

    }
}
</script>

<style scoped>
/* 确保全屏显示，覆盖所有可能的父容器限制 */
* {
    box-sizing: border-box;
}

/* 全屏覆盖层 */
.fullscreen-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: #f5f5f5;
    overflow: hidden;
}

/* 基础布局样式 - 全屏显示 */
.ship-detail {
    width: 100vw !important;
    height: 100vh !important;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 9999 !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden;
    max-width: none !important;
    max-height: none !important;
    min-width: 100vw !important;
    min-height: 100vh !important;
}

.mobile-menu-header {
    display: none;
    position: relative;
    background-color: #1890ff;
    height: 40px;
    align-items: center;
    justify-content: flex-end;
    padding: 8px 20px 8px 0;
}

@media (max-width: 768px) {
    .mobile-menu-header {
        display: flex;
    }
}

.menu-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: white;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    position: relative;
    right: 0;
}

.back-btn {
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
    width: 30px;
    height: 30px;
    padding: 0;
}

.back-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    border-top: 2px solid white;
    border-left: 2px solid white;
    transform: translate(-50%, -50%) rotate(-45deg);
}

.menu-btn:hover,
.back-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.detail-layout {
    display: flex;
    flex: 1;
    background-color: white;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* 左侧菜单样式 */
.menu-sidebar {
    width: 160px;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.sidebar-header {
    display: none;
    padding: 15px;
    background-color: #1890ff;
    color: white;
    justify-content: space-between;
    align-items: center;
}

.close-menu-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
}

.menu-item {
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s;
    font-size: 14px;
}

.menu-item:hover {
    background-color: #e9ecef;
}

.menu-item.active {
    background-color: #1890ff;
    color: white;
}

/* 移动端菜单样式 */
@media (max-width: 768px) {
    .menu-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 1001;
        transform: translateX(-100%);
        width: 250px;
    }

    .menu-sidebar.mobile-visible {
        transform: translateX(0);
    }

    .sidebar-header {
        display: flex;
    }
}

/* 右侧内容区域 */
.content-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: white;
    width: 100%;
}

.info-section {
    display: flex;
    flex-direction: column;
}

.info-section h2 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 20px;
    font-weight: bold;
    border-bottom: 2px solid #1890ff;
    padding-bottom: 10px;
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.toolbar-left {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: nowrap; /* 防止换行 */
    min-width: 0; /* 允许弹性收缩 */
}

/* 时间范围选择器样式 */
.time-range-selector {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-right: 15px;
    flex-shrink: 0; /* 防止压缩 */
}

.time-range-selector label {
    font-size: 13px;
    color: #495057;
    font-weight: 500;
    white-space: nowrap;
    flex-shrink: 0; /* 防止标签被压缩 */
}

.time-range-select {
    padding: 5px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: white;
    color: #495057;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 110px; /* 减小最小宽度 */
    max-width: 130px; /* 限制最大宽度 */
}

.time-range-select:hover {
    border-color: #1890ff;
}

.time-range-select:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.tool-btn {
    padding: 6px 12px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.tool-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.tool-btn.active {
    background-color: #1890ff;
    border-color: #1890ff;
    color: white;
}

.count-display {
    font-size: 14px;
    color: #666;
    font-weight: bold;
}

/* 竖向表格样式 */
.vertical-table-container {
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.loading-message, .empty-message {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: #666;
    font-size: 14px;
}

/* 在线船舶 - 保持原有的垂直表格样式 */
.vertical-table {
    display: flex;
    overflow: hidden;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

/* 离线船舶 - 水平表格样式 */
.offline-horizontal-table {
    overflow: hidden;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    max-height: 600px;
    overflow-y: auto; /* 垂直滚动 */
}

.offline-ship-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.offline-ship-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 12px 8px;
    text-align: center;
    border-bottom: 2px solid #dee2e6;
    border-right: 1px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
    white-space: normal; /* 允许表头换行 */
    word-wrap: break-word; /* 长单词换行 */
    vertical-align: middle; /* 垂直居中 */
}

.offline-ship-table th:last-child {
    border-right: none;
}

.offline-ship-table td {
    padding: 12px 8px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    border-right: 1px solid #dee2e6;
    color: #495057;
    background-color: white;
    white-space: normal; /* 允许换行 */
    word-wrap: break-word; /* 长单词换行 */
    vertical-align: middle; /* 垂直居中 */
}

.offline-ship-table td:last-child {
    border-right: none;
}

.offline-ship-row {
    cursor: pointer;
    transition: background-color 0.2s;
}

.offline-ship-row:hover {
    background-color: #f0f8ff;
}

.offline-ship-row:hover td {
    background-color: #f0f8ff;
}

/* 船名单元格特殊样式 */
.ship-name-cell {
    color: #007bff !important;
    font-weight: bold;
    cursor: pointer;
}

.ship-name-cell:hover {
    color: #0056b3 !important;
    text-decoration: underline;
}

/* 离线时间单元格特殊样式 - 用于48小时外的船舶 */
.offline-time-cell {
    background-color: #dc3545 !important;
    color: white !important;
    font-weight: bold;
}

/* 离线原因列样式 - 自适应内容高度，允许换行 */
.reason-cell {
    white-space: normal !important; /* 允许换行 */
    word-wrap: break-word !important; /* 长单词换行 */
    text-align: center !important; /* 居中对齐 */
    max-width: 150px; /* 限制最大宽度 */
    vertical-align: middle; /* 垂直居中 */
}

/* 北斗号列样式 - 限制宽度，允许换行 */
.bdid-cell {
    max-width: 100px !important; /* 限制最大宽度 */
    word-wrap: break-word !important; /* 允许在单词内换行 */
    word-break: break-all !important; /* 强制换行，即使在单词中间 */
    white-space: normal !important; /* 允许换行 */
    line-height: 1.3 !important; /* 设置行高，让多行显示更紧凑 */
    padding: 6px 4px !important; /* 调整内边距 */
    vertical-align: middle !important; /* 垂直居中对齐 */
    text-align: center !important; /* 水平居中对齐 */
}

/* 空数据占位行样式 */
.empty-row td {
    color: #999 !important; /* 灰色文字 */
    font-style: italic; /* 斜体 */
    background-color: #f9f9f9 !important; /* 浅灰色背景 */
}

.empty-row:hover td {
    background-color: #f9f9f9 !important; /* 悬停时保持浅灰色背景 */
    cursor: default; /* 默认鼠标样式，不显示点击手势 */
}

/* 左侧表头列 */
.table-headers {
    width: 120px;
    min-width: 120px;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
}

.header-cell {
    padding: 12px 8px;
    font-weight: bold;
    font-size: 13px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    color: #495057;
    min-height: 40px;
    display: flex;
    align-items: flex-start; /* 内容顶部对齐 */
    justify-content: center;
    background-color: #f8f9fa;
}



.header-cell:last-child {
    border-bottom: none;
}

/* 右侧数据区域 */
.table-data-container {
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
}

.table-data {
    display: flex;
    height: 100%;
    min-width: fit-content;
}

/* 每一列数据 */
.data-column {
    min-width: 120px;
    width: auto; /* 自动宽度 */
    border-right: 1px solid #dee2e6;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    flex-direction: column;
}

.data-column:hover {
    background-color: #f0f8ff;
}

.data-column:last-child {
    border-right: none;
}

/* 数据单元格 */
.data-cell {
    padding: 12px 8px;
    font-size: 12px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    color: #495057;
    min-height: 40px;
    display: flex;
    align-items: flex-start; /* 内容顶部对齐 */
    justify-content: center;
    word-break: break-all;
    background-color: white;
}



.data-cell:last-child {
    border-bottom: none;
}

/* 船名单元格特殊样式 */
.ship-name-cell {
    font-weight: bold;
    color: #1890ff;
    background-color: #f0f8ff;
}

/* 离线时间单元格特殊样式 - 用于48小时外的船舶 */
.offline-time-cell {
    background-color: #dc3545 !important;
    color: white !important;
    font-weight: bold;
}



/* 统计信息网格样式 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.info-item .label {
    font-weight: bold;
    color: #495057;
}

.info-item .value {
    color: #1890ff;
    font-weight: bold;
}

.main-content {
    flex: 1;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .content-area {
        padding: 10px;
    }

    .toolbar {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .toolbar-left {
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 8px;
    }

    /* 移动端时间范围选择器样式 */
    .time-range-selector {
        flex-direction: row; /* 保持水平布局 */
        gap: 4px;
        margin-right: 0;
        margin-bottom: 5px;
        align-items: center;
        flex-shrink: 0;
    }

    .time-range-selector label {
        font-size: 11px;
    }

    .time-range-select {
        font-size: 11px;
        padding: 3px 6px;
        min-width: 90px; /* 移动端更小的宽度 */
        max-width: 100px;
    }

    .vertical-table-container {
        padding: 5px 0;
    }

    .table-headers {
        width: 100px;
        min-width: 100px;
    }

    .header-cell {
        padding: 8px 4px;
        font-size: 11px;
        min-height: 32px;
    }

    .data-column {
        min-width: 100px;
        width: 100px;
    }

    .data-cell {
        padding: 8px 4px;
        font-size: 11px;
        min-height: 32px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    /* 移动端在线船舶表格样式 */
    .vertical-table-container {
        padding: 5px 0;
    }

    .table-headers {
        width: 100px;
        min-width: 100px;
    }

    .header-cell {
        padding: 8px 4px;
        font-size: 11px;
        min-height: 32px;
    }

    .data-column {
        min-width: 100px;
        width: 100px;
    }

    .data-cell {
        padding: 8px 4px;
        font-size: 11px;
        min-height: 32px;
    }

    /* 移动端离线船舶表格样式 */
    .offline-horizontal-table {
        font-size: 10px;
    }

    .offline-ship-table th {
        padding: 6px 4px;
        font-size: 10px;
        white-space: normal;
        word-wrap: break-word;
        vertical-align: middle;
    }

    .offline-ship-table td {
        padding: 6px 4px;
        font-size: 10px;
        white-space: normal;
        word-wrap: break-word;
        vertical-align: middle;
    }

    .reason-cell {
        max-width: 100px;
        font-size: 9px;
    }

    /* 移动端北斗号列样式 */
    .bdid-cell {
        max-width: 80px !important; /* 移动端更小的宽度 */
        font-size: 9px !important; /* 更小的字体 */
        line-height: 1.2 !important; /* 更紧凑的行高 */
        padding: 4px 2px !important; /* 更小的内边距 */
        vertical-align: middle !important; /* 垂直居中对齐 */
    }

    /* 移动端空数据占位行样式 */
    .empty-row td {
        color: #999 !important;
        font-style: italic;
        background-color: #f9f9f9 !important;
        font-size: 10px;
    }
}

/* 桌面端隐藏移动端菜单 */
@media (min-width: 769px) {
    .mobile-menu-header {
        display: none !important;
    }
}
</style>
